<?php

namespace App\Services\PetAnalysis;

use App\Services\AliBailian\AliBailianService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class IngredientAnalysisService extends BaseAnalysisService
{
    /**
     * 构造函数
     *
     * @param AliBailianService $aliBailianService
     */
    public function __construct(AliBailianService $aliBailianService)
    {
        parent::__construct($aliBailianService);

        $this->type = 'ingredient';
        $this->name = '宠物粮成分表分析';
        $this->description = '分析宠物粮成分对宠物的宠物食品中的添加剂及其潜在健康风险';
    }

    /**
     * 执行成分表分析
     *
     * @param string $query 用户查询
     * @param array|null $images 图片数组
     * @param int|null $petId 宠物ID
     * @param string|null $sessionId 会话ID
     * @param bool $stream 是否使用流式响应
     * @param string|null $bodyPart 分析的身体部位（用于健康分析）
     * @param array|null $symptoms 症状列表（用于健康分析）
     * @param array|null $petsInfo 多宠物信息数组（用于多宠物分析）
     * @return mixed 分析结果
     */
    public function analyze(string $query, ?array $images = null, ?int $petId = null, ?string $sessionId = null, bool $stream = false, ?string $bodyPart = null, ?array $symptoms = null, ?array $petsInfo = null): mixed
    {
        try {
            // 记录开始时间
            $startTime = microtime(true);
            // 获取宠物信息
            $petInfo = $this->getPetInfo($petId);

            // 记录提示词构建开始时间
            $promptStartTime = microtime(true);

            // 构建提示词，使用模板格式并传入多宠物信息
            // 始终使用模板格式，不考虑stream参数
            $prompt = $this->buildIngredientAnalysisPrompt($petInfo, $petsInfo, $images);

            // 记录提示词构建耗时
            $promptBuildTime = microtime(true) - $promptStartTime;

            // 添加用户查询
            $finalPrompt = $prompt . "\n\n用户问题: " . $query;

            // 记录AI调用开始时间
            $aiCallStartTime = microtime(true);

            // 调用AI服务，根据参数决定是否使用流式响应
            // 不使用上下文，只传递提示词和图片，加快响应速度
            $response = $this->aiService->chat(
                $finalPrompt,
                $images,
                $sessionId,
                0.7,
                $stream // 根据参数决定是否使用流式响应
            );

            // 记录AI调用耗时
            $aiCallTime = microtime(true) - $aiCallStartTime;

            // 如果是流式响应，直接返回响应对象
            if ($stream) {
                return $response;
            }

            // 如果不是流式响应，处理响应内容
            $content = $response['choices'][0]['message']['content'] ?? null;

            if (empty($content)) {
                return [
                    'status' => 'error',
                    'message' => '获取分析结果失败',
                    'analysis_type' => $this->getType()
                ];
            }

            // 记录结束时间和耗时
            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            $promptBuildTime = round($promptBuildTime ?? 0, 2);
            $aiCallTime = round($aiCallTime, 2);

            \Illuminate\Support\Facades\Log::info("IngredientAnalysisService 耗时分析", [
                'total_time' => $duration,
                'prompt_build_time' => $promptBuildTime,
                'ai_call_time' => $aiCallTime,
                'other_time' => round($duration - $promptBuildTime - $aiCallTime, 2),
                'has_images' => !empty($images),
                'image_count' => count($images ?? [])
            ]);

            // 返回格式化的响应，保持与流式响应相同的格式
            return [
                'status' => 'success',
                'message' => '分析成功',
                'data' => [
                    'content' => $content,
                    'session_id' => $response['session_id'] ?? null,
                    'is_new_session' => $response['is_new_session'] ?? false
                ],
                // 移除pet_info字段，由控制器统一添加
                'analysis_type' => $this->getType(),
                'performance' => [
                    'total_time' => $duration,
                    'prompt_build_time' => $promptBuildTime,
                    'ai_call_time' => $aiCallTime
                ]
            ];

        } catch (\App\Exceptions\AIServiceException $e) {
            // 直接抛出AI服务异常
            throw $e;
        } catch (\Exception $e) {
            Log::error('成分表分析失败: ' . $e->getMessage());
            // 将异常转换为AI服务异常并抛出
            throw new \App\Exceptions\AIServiceException(
                '成分表分析失败: ' . $e->getMessage(),
                'ingredient_analysis_error',
                $sessionId ?? null,
                [
                    'original_error' => $e->getMessage(),
                    'analysis_type' => $this->getType()
                ]
            );
        }
    }

    /**
     * 构建成分表分析提示词
     *
     * @param array|null $petInfo
     * @param array|null $petsInfo 多宠物信息数组
     * @param array $images 图片数组
     * @return string
     */
    protected function buildIngredientAnalysisPrompt(?array $petInfo, ?array $petsInfo = null, array $images = []): string
    {
        $prompt = "你是一个专业的宠物食品成分分析专家，请分析用户上传的宠物粮成分表图片，识别其中的添加剂成分并评估其对宠物的潜在健康风险。请使用纯正的香港粤语（广东话）回复，不要使用普通话或书面语。回答要自然流畅，符合香港本地人的表达方式，语气要亲切友好。\n\n如果用户提供了多只宠物的信息，请智能分析每只宠物的情况，并将有相同分析结果的宠物合并到一条回复中，避免重复内容。例如：如果3只宠物都适合同一款宠物粮，请写成'宠物A、宠物B、宠物C都适合食用'，而不是分别为每只宠物写重复的回复。\n\n";

        // 添加知识库内容
        $prompt .= "以下是宠物食品中常见添加剂及其潜在健康风险的专业知识库：\n\n";

        // 化学防腐剂
        $prompt .= "**化學防腐劑：**\n";
        $prompt .= "- 丁基羥基茴香醚(BHA)：合成抗氧化防腐劑；被分類為致癌物，動物研究顯示與腫瘤形成及潛在生殖毒性相關。\n";
        $prompt .= "- 丁基羥基甲苯(BHT)：與BHA相似；在實驗動物中引起腎臟和肝臟損傷，被認為是致癌物。\n";
        $prompt .= "- 乙氧喹(Ethoxyquin)：源自農藥的防腐劑，已禁止用於人類食品；與肝腎損傷、免疫抑制及癌症（如白血病）相關。\n";
        $prompt .= "- 亞硝酸鈉(Sodium Nitrite)：用於著色和防腐；在高劑量下可引起血紅蛋白病，與維生素C和E結合時與癌症相關。\n";
        $prompt .= "- 丙酸鈣(Calcium Propionate)：防腐劑，防止黴菌生長；可能引發消化不適或過敏，尤其對敏感寵物，可能導致腹脹或皮膚問題。\n\n";

        // 填充劑和過敏原
        $prompt .= "**填充劑和過敏原：**\n";
        $prompt .= "- 玉米（及玉米糖漿）：見填充劑和碳水化合物來源；為過敏原，易導致消化困難、肥胖、糖尿病及黴菌毒素污染引起的肝衰竭。\n";
        $prompt .= "- 大豆製品：蛋白質填充劑；具雌激素效應，擾亂內分泌功能，引起脹氣、腹脹和過敏反應。\n";
        $prompt .= "- 麩質：小麥來源的黏合劑；常見過敏原，導致耳部感染、皮膚熱點和酵母菌過度生長。\n\n";

        // 副產品和低質蛋白質
        $prompt .= "**副產品和低質蛋白質：**\n";
        $prompt .= "- 肉骨粉/動物副產品：來自不明或低質來源（如患病動物）的加工品；營養生物利用率低，可能暴露於安樂死藥物（如戊巴比妥）。\n";
        $prompt .= "- 動物脂肪(未明確來源)：可能來自不明或低質來源；可能含有污染物（如二噁英）或氧化脂肪，導致炎症或心血管問題。\n\n";

        // 其他添加劑
        $prompt .= "**其他有害添加劑：**\n";
        $prompt .= "- 卡拉膠(Carrageenan)：罐頭食品中的增稠劑；誘發腸道炎症，導致腸易激綜合症（IBS）或炎症性腸病（IBD）。\n";
        $prompt .= "- 人工甜味劑（如木糖醇、山梨醇）：風味增強劑；對狗高度毒性，導致低血糖、肝衰竭和癲癇發作。\n";
        $prompt .= "- 食用色素（如紅色40號、黃色5/6號）：合成色素；與過動症、過敏、過敏反應和癌症相關。\n";
        $prompt .= "- 丙二醇(Propylene Glycol)：保濕劑；對貓有毒（引起貧血），長期暴露可能導致狗的肝臟問題。\n";
        $prompt .= "- 雙酚A(BPA)：罐頭內襯；內分泌干擾物，與代謝變化、腸道微生物群紊亂和貓甲狀腺功能亢進相關。\n";
        $prompt .= "- 甲萘醌鈉(Menadione Sodium Bisulfite)：合成維生素K；高劑量有毒，損害腎臟、肝臟和肺部。\n";
        $prompt .= "- 焦糖色素(Caramel Color)：用於增強顏色；含4-甲基咪唑（4-MEI），被列為潛在致癌物，可能增加癌症風險。\n";
        $prompt .= "- 磷酸鹽(Phosphates)：用作乳化劑或穩定劑；過量攝入可能導致腎臟負擔，加劇慢性腎衰竭。\n";
        $prompt .= "- 瓜爾膠(Guar Gum)：增稠劑；可能引發腸道不適或阻塞，導致腹瀉或脹氣。\n";
        $prompt .= "- 聚山梨酯(Polysorbates)：用作乳化劑；可能引起腸道炎症或過敏反應，影響腸道微生物群平衡。\n";
        $prompt .= "- 硫酸鈉(Sodium Sulfate)：用於調節質地或作為填充劑；高劑量可能導致電解質失衡，加重腎臟負擔。\n";
        $prompt .= "- 人造香料(Artificial Flavors)：化學合成的風味劑；可能引發過敏反應或消化問題，長期影響尚未充分研究。\n";
        $prompt .= "- 味精(MSG)：增味劑；可能導致神經興奮毒性，引發過敏、行為異常或消化不良。\n";
        $prompt .= "- 糖蜜(Molasses)：用於增甜或提高適口性；高糖分可能導致肥胖、牙齒問題或糖尿病風險。\n";
        $prompt .= "- 氯化膽鹼(Choline Chloride)：合成營養添加劑；過量可能引起消化道刺激或神經系統問題。\n\n";

        // 使用模板格式，添加颜色标注
        $prompt .= "请按照以下格式回复，使用纯正的香港粤语（广东话）：\n\n";

        $prompt .= "查詢對象: [全部/宠物名称列表]\n\n";

        // 根据是否有图片来决定提示词
        if (!empty($images)) {
            $prompt .= "圖片中既宠物粮品牌: [品牌名称]\n\n";
            $prompt .= "成分表識別結果: [从图片中识别出的主要成分列表]\n\n";
        } else {
            $prompt .= "查詢既宠物粮: [宠物粮名称或品牌]\n\n";
        }

        $prompt .= "風險評估: \n";
        $prompt .= "请根据实际检测到的添加剂情况进行风险评估：\n";
        // 如果是多只宠物
        if (!empty($petsInfo) && count($petsInfo) > 1) {
            $prompt .= "请根据每只宠物的情况分析，并将有相同结果的宠物合并到一条回复中：\n";
            $prompt .= " ● 如果检测到严重有害添加剂（如BHA、BHT、乙氧喹、人工甜味剂等）：<span style=\"color: red;\">[宠物A、宠物B]高風險！</span> [具体说明检测到哪些有害添加剂及其危害]\n";
            $prompt .= " ● 如果检测到中等风险添加剂（如玉米糖浆、大豆制品、人工色素等）：<span style=\"color: orange;\">[宠物C、宠物D]中等風險。</span> [具体说明检测到哪些添加剂及其潜在影响]\n";
            $prompt .= " ● 如果未检测到明显有害添加剂：<span style=\"color: green;\">[宠物E]低風險。</span> [说明成分相对安全，主要为天然成分]\n";
            $prompt .= "注意：请将分析结果相同的宠物合并在一条回复中，用顿号（、）分隔宠物名称，避免重复的回复内容。\n\n";
        }
        // 如果是单只宠物
        else {
            $prompt .= " ● 如果检测到严重有害添加剂：<span style=\"color: red;\">[高風險]</span> - [具体说明检测到哪些严重有害添加剂]\n";
            $prompt .= " ● 如果检测到中等风险添加剂：<span style=\"color: orange;\">[中等風險]</span> - [具体说明检测到哪些中等风险添加剂]\n";
            $prompt .= " ● 如果未检测到明显有害添加剂：<span style=\"color: green;\">[低風險]</span> - [说明成分相对安全，主要为天然成分]\n\n";
        }

        $prompt .= "<span style=\"color: #4CAF50;\">檢測到既添加劑詳情: </span>\n";
        $prompt .= "请根据实际检测情况回复：\n";
        // 如果是多只宠物
        if (!empty($petsInfo) && count($petsInfo) > 1) {
            $prompt .= " ● 如果检测到有害添加剂：[具体添加剂名称]--[对宠物的具体危害及影响的宠物类型]\n";
            $prompt .= " ● 如果未检测到有害添加剂：未檢測到明顯有害添加劑，成分以天然成分為主\n";
            $prompt .= " ● 注意：只列出实际检测到的添加剂，没有检测到就说明没有\n\n";
        } else {
            $prompt .= " ● 如果检测到有害添加剂：[具体添加剂名称及其具体危害]\n";
            $prompt .= " ● 如果未检测到有害添加剂：未檢測到明顯有害添加劑，成分相對安全\n";
            $prompt .= " ● 注意：只列出实际检测到的添加剂，没有检测到就说明成分安全\n\n";
        }

        $prompt .= "<span style=\"color: #4CAF50;\">建議措施: </span>\n";
        $prompt .= "请根据实际检测结果给出相应建议：\n";
        // 如果是多只宠物
        if (!empty($petsInfo) && count($petsInfo) > 1) {
            $prompt .= " ● 如果检测到严重有害添加剂：[受影响宠物名称]--立即停止喂食，更换无害添加剂的宠物粮品牌\n";
            $prompt .= " ● 如果检测到中等风险添加剂：[受影响宠物名称]--减少喂食频率，寻找更安全的替代品\n";
            $prompt .= " ● 如果未检测到有害添加剂：[所有宠物]--可以繼續使用，成分相對安全\n";
            $prompt .= "注意：只有检测到有害添加剂才需要给出警告建议，没有检测到就说明可以安全使用。\n\n";
        } else {
            $petName = $petInfo ? $petInfo['name'] : '宠物';
            $prompt .= " ● 如果检测到有害添加剂：立即停止使用，更换安全的宠物粮\n";
            $prompt .= " ● 如果未检测到有害添加剂：可以繼續使用，{$petName}食用相對安全\n";
            $prompt .= " ● 定期监测宠物健康状况\n\n";
        }

        $prompt .= "<span style=\"color: #2196F3;\">安全成分建議: </span>\n";
        $prompt .= " ● 推薦天然防腐劑：維生素E（生育酚）、維生素C（抗壞血酸）、迷迭香提取物\n";
        $prompt .= " ● 推薦優質蛋白質：新鮮雞肉、魚肉、羊肉等明確標示來源的肉類\n";
        $prompt .= " ● 應避免成分：BHA/BHT、乙氧喹、人工甜味劑、人工色素、肉骨粉\n\n";

        $prompt .= "<span style=\"color: #FF5733;\">注意事項: </span>\n";
        $prompt .= " ● 如需更換宠物粮，請逐漸過渡，避免腸胃不適\n";
        $prompt .= " ● 定期觀察宠物食慾、精神狀態和排便情況\n";
        $prompt .= " ● 如出現過敏或不適症狀，請立即停止使用並諮詢獸醫\n\n";

        // 以下是给AI的指导，使用特殊标记包裹，确保AI不会直接返回这些内容
        $prompt .= "<instructions>\n";
        $prompt .= "请注意：\n";
        $prompt .= "1. 如果无法识别成分表，请在成分表识别结果部分回答\"无法识别成分表: 原因\"。\n";
        $prompt .= "2. **多宠物统一回复规则（重要）：**\n";
        $prompt .= "   - 风险评估必须基于实际检测到的添加剂，不是预设的风险等级\n";
        $prompt .= "   - 如果未检测到有害添加剂：'所有宠物都是低風險，可以繼續使用'\n";
        $prompt .= "   - 如果检测到中等风险添加剂：'所有宠物都是中等風險，建議謹慎使用'\n";
        $prompt .= "   - 如果检测到严重有害添加剂：'所有宠物都是高風險，建議立即停止使用'\n";
        $prompt .= "   - 只有当不同宠物对同一添加剂有不同敏感性时，才分别分析\n";
        $prompt .= "   - 例如：木糖醇对狗高风险，对猫中等风险\n";
        $prompt .= "   - 在建议措施部分，相同风险等级的宠物也可以合并建议\n";
        $prompt .= "3. 请确保回复的格式与模板一致，使用纯正的香港粤语（广东话）回复。\n";
        $prompt .= "4. 对于不同宠物的情况，请根据宠物的品种、年龄、体重、健康状况等特征给出个性化的风险评估。\n";
        $prompt .= "5. **重要：只有实际检测到有害添加剂时，才说明有害；未检测到就说明安全。**\n";
        $prompt .= "6. 如果检测到有害添加剂，请明确指出具体是哪些添加剂并解释危害。\n";
        $prompt .= "7. 如果未检测到有害添加剂，请明确说明成分相对安全，可以继续使用。\n";
        $prompt .= "7. 请使用项目符号（●）来增强可读性，并保持格式美观。\n";
        $prompt .= "8. 请使用专业但亲切的语气，就像在和关心宠物健康的主人聊天一样。\n";
        $prompt .= "9. 请确保回复中没有乱码或格式错误。\n";
        $prompt .= "10. 请为重要内容添加颜色标注：\n";
        $prompt .= "   - 低风险：<span style=\"color: green;\">绿色</span>\n";
        $prompt .= "   - 中等风险：<span style=\"color: orange;\">橙色</span>\n";
        $prompt .= "   - 高风险：<span style=\"color: red;\">红色</span>\n";
        $prompt .= "   - 有害添加剂详情和建议措施：<span style=\"color: #4CAF50;\">绿色</span>\n";
        $prompt .= "   - 安全成分建议：<span style=\"color: #2196F3;\">蓝色</span>\n";
        $prompt .= "   - 注意事项：<span style=\"color: #FF5733;\">红橙色</span>\n";
        $prompt .= "11. **重要：请使用简洁明了的文字表达，避免冗长的句子和重复的表述。每个要点都要言简意赅，直接说重点。**\n";
        $prompt .= "12. **文字风格：用词精练，表达直接，避免过多的修饰词和解释性语言。**\n";
        $prompt .= "13. **成分识别要求：请仔细识别图片中的所有成分，特别关注添加剂、防腐剂、色素等化学成分。**\n";
        $prompt .= "14. **风险评估标准（必须严格遵守）：**\n";
        $prompt .= "   - 高风险：实际检测到BHA、BHT、乙氧喹、人工甜味剂等严重有害添加剂\n";
        $prompt .= "   - 中等风险：实际检测到玉米糖浆、大豆制品、人工色素等可能引起过敏的成分\n";
        $prompt .= "   - 低风险：未检测到明显有害添加剂，主要为天然成分\n";
        $prompt .= "   - **关键：风险评估必须基于实际检测结果，不能预设风险等级**\n";
        $prompt .= "仅回答关于宠物粮成分分析的问题，不要回答与成分分析无关的内容。\n";
        $prompt .= "</instructions>\n\n";

        // 添加多宠物信息（如果有）
        if (!empty($petsInfo)) {
            $prompt .= "宠物信息:\n";
            foreach ($petsInfo as $index => $pet) {
                $prompt .= "宠物" . ($index + 1) . ":\n";
                $prompt .= "- 名称: " . $pet['name'] . "\n";
                $prompt .= "- 类型: " . $pet['type'] . "\n";
                $prompt .= "- 品种: " . $pet['breed'] . "\n";
                $prompt .= "- 性别: " . $pet['sex'] . "\n";

                if (isset($pet['age'])) {
                    $prompt .= "- 年龄: " . $pet['age'] . " 岁\n";
                }

                if (isset($pet['weight'])) {
                    $prompt .= "- 体重: " . $pet['weight'] . " 公斤\n";
                }

                // 添加特定宠物类型的敏感性说明
                $petTypeEn = strtolower($pet['type_en'] ?? '');

                if (Str::contains($petTypeEn, 'dog')) {
                    $prompt .= "- 注意：狗对人工甜味剂（如木糖醇）极度敏感，可能致命\n";
                    $prompt .= "- 注意：狗容易对玉米、大豆等填充剂过敏\n";
                } elseif (Str::contains($petTypeEn, 'cat')) {
                    $prompt .= "- 注意：猫对丙二醇有毒性反应，会引起贫血\n";
                    $prompt .= "- 注意：猫容易受BPA影响，可能导致甲状腺功能亢进\n";
                }

                $prompt .= "\n";
            }
        } elseif ($petInfo) {
            // 添加单个宠物信息（如果没有多宠物信息但有单个宠物信息）
            $prompt .= "宠物信息:\n";
            $prompt .= "- 名称: " . $petInfo['name'] . "\n";
            $prompt .= "- 类型: " . $petInfo['type'] . "\n";
            $prompt .= "- 品种: " . $petInfo['breed'] . "\n";
            $prompt .= "- 性别: " . $petInfo['sex'] . "\n";

            if (isset($petInfo['age'])) {
                $prompt .= "- 年龄: " . $petInfo['age'] . " 岁\n";
            }

            if (isset($petInfo['weight'])) {
                $prompt .= "- 体重: " . $petInfo['weight'] . " 公斤\n";
            }

            if (isset($petInfo['weight_status'])) {
                $prompt .= "- 体重状态: " . $petInfo['weight_status'] . "\n";
            } else {
                $prompt .= "- 体重状态: 正常\n";
            }

            $prompt .= "- 是否绝育: " . ($petInfo['neutered'] ? '是' : '否') . "\n";
            $prompt .= "- 是否怀孕: " . ($petInfo['is_pregnant'] ? '是' : '否') . "\n";
            $prompt .= "- 是否生病: " . ($petInfo['is_ill'] ? '是' : '否') . "\n";

            if (!empty($petInfo['special_conditions'])) {
                $prompt .= "- 特殊情况: " . implode(', ', $petInfo['special_conditions']) . "\n";
            }

            // 添加特定宠物类型的敏感性说明
            $petTypeEn = strtolower($petInfo['type_en'] ?? '');

            if (Str::contains($petTypeEn, 'dog')) {
                $prompt .= "\n特别注意：对于狗，以下添加剂特别危险：人工甜味剂（木糖醇等）可能致命，BHA/BHT等防腐剂有致癌风险，玉米/大豆等填充剂容易引起过敏。";
            } elseif (Str::contains($petTypeEn, 'cat')) {
                $prompt .= "\n特别注意：对于猫，以下添加剂特别危险：丙二醇会引起贫血，BPA会影响甲状腺功能，卡拉胶会引起肠道炎症，人工色素和防腐剂对猫的肝肾负担较大。";
            }
        } else {
            $prompt .= "用户没有指定特定宠物，请提供适用于一般猫和狗的成分分析，并特别标注对不同宠物类型的特殊风险。";
        }

        return $prompt;
    }
}
